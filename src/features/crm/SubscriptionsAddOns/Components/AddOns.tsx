import { IAddOns, ICollectionAddOns } from "@services/subscription/accountId/interface/IAddOns";
import { Box, Button, Grid, Skeleton, Tooltip, Typography } from "@mui/material";
// import { keycloakUtils } from "itsf-ui-common";
import { useTranslation } from "react-i18next";
import { AddonsDetailDialog } from "@common/Dialog/InformationDetailDialog/AddonsDetailDialog";
import { useEffect, useState } from "react";
import { CancelAddOnDialog } from "@common/Dialog/CancelAddOnDialog/CancelAddOnDialog";
import ReScheduleConfirmationModal from "@common/styleComponents/OrderConfirmationModal/ReScheduleConfirmationModal";
import { IBillCycleState } from "@features/crm/AccountDetails/AccountDetailsHeader/Billing/BillCycle/useBillCycle";
// import OtpHandlerDialog from "@common/OtpHandlerDialog/OtpHandlerDialog";
import { useOtpHandlerDialog } from "@common/OtpHandlerDialog/useOtpHandlerDialog";
import { TContactDetails } from "@features/crm/CustomerDetails/ICustomerDetails";
import { HandleErrorAndLoading, useSnackBar } from "@common";
import { ITigoCbsAccountsResponse } from "@modules/tigoSalesFacade/interfaces/responses/ITigoCbsAccountsResponse";
// import { ADDONS_PERMISSION, ADDONS_STATUS, RULES_OF_USER } from "@constants";
import { ADDONS_STATUS } from "@constants";
import InfoIcon from "@mui/icons-material/Info";
import { isEmpty } from "lodash";
import dayjs from "dayjs";
import { useAddonStore } from "../useAddonStore";
import { getConfig } from "@config";
import { centsToPrice } from "itsf-ui-common";

//import { useAddNewOffer } from "./AddNewOffer/useAddNewOffer";
// import { useAddOnsCancellation } from "./AddOns/useAddOnsCancellation"; // Ya no se usa aquí

import { ISubscriptionsCollection } from "@services/subscription/accountId/interface/ISubscriptionsAccountIds";
import { ITigoRiskManagementResponse } from "@modules/tigoSalesFacade/interfaces/responses/ITigoRiskManagementResponse";
import { IAddon } from "@features/crm/Addons/IAddon";

interface IProps {
    data: ICollectionAddOns;
    contactDetails: TContactDetails | undefined;
    classes: Record<string, string>;
    index: number;
    handleActiveRetention: (addonCode: string, addonId: number) => void;
    onClickOpenOffer: () => void;
    validateCancelAddOns: (
        activateDate: string,
        _addOnStatus: string,
        billCycleEndDate?: Date,
        _billCycleOpenDate?: Date
    ) => boolean;
    addOnsServices: IAddOns;
    actualBillCycle: { billCycleOpenDate: Date; billCycleEndDate: Date };
    removeAddOn: (addonsId: number, serviceId: number, cycleDate: string) => Promise<number | string>;
    billCycleState: IBillCycleState;
    getBillCycle: () => Promise<void>;
    billCycleId: string;
    accountsCbs: ITigoCbsAccountsResponse | undefined;
    calculateCycleDate: (cycleDay: number) => dayjs.Dayjs;
    handleShowSchedule: (show: boolean) => void;
    setOrderIdForSchedule: React.Dispatch<React.SetStateAction<string>>;

    //Parametros para cancel addon
    subscriptions: ISubscriptionsCollection | undefined;
    setSuccessRisk: React.Dispatch<React.SetStateAction<ITigoRiskManagementResponse | null | undefined>>;
    setAddNewAddOns: React.Dispatch<React.SetStateAction<boolean>>;

    // Funciones centralizadas del hook useAddOnsCancellation
    handleAddClick: (addon: ICollectionAddOns) => void;
    callWfeFacadeCancelationAddon: ({
        contactDetails,
        subscriptionsList,
        appointmentConfirmation,
        addonId,
        serviceId,
        setOrderIdForSchedule,
    }: {
        contactDetails: TContactDetails | undefined;
        subscriptionsList: any[];
        appointmentConfirmation?: boolean;
        addonId: number;
        serviceId: number;
        setOrderIdForSchedule: React.Dispatch<React.SetStateAction<string>>;
    }) => Promise<void>;
}

const titleDialog = "Detalle de Addons";

export default function AddOns({
    data,
    classes,
    // validateCancelAddOns,
    addOnsServices,
    removeAddOn,
    billCycleState,
    // billCycleId,
    accountsCbs,
    getBillCycle,
    handleShowSchedule,
    setOrderIdForSchedule,

    subscriptions,
    setSuccessRisk,
    setAddNewAddOns,
    contactDetails,

    // Funciones centralizadas del hook
    handleAddClick,
    callWfeFacadeCancelationAddon,
}: IProps) {
    // const { id, charges, description, activatedAt, terminatedAt, status } = data;
    const { id, description, activatedAt, terminatedAt, status, charges } = data;
    const { t } = useTranslation(["common", "customer"]);
    const [isModalOpen, setIsModalOpen] = useState(false);
    const [isOpen, setIsOpen] = useState(false);
    const [isCancellable, setIsCancellable] = useState(false);
    const [isMaterial, setIsMaterial] = useState(false);
    const { setSnackBarError } = useSnackBar();
    // const [cancelMessage, setCancelMessage] = useState("");
    const [cancelMessage] = useState("");
    const [orderCancelled, setOrderCancelled] = useState<number | string>();
    const [showCancelDialog, setShowCancelDialog] = useState(false);
    const [otpApproved, setOtpApproved] = useState(false);

    // const { open, setOpen, otpMethod, setOtpMethod, otpAlreadyValidated, otpEmailSuccess, otpSmsSuccess, ...props } =
    //     useOtpHandlerDialog();

    // Ya no necesitamos llamar al hook aquí porque las funciones vienen como props



    const { setOpen, otpEmailSuccess, otpSmsSuccess } = useOtpHandlerDialog();

    const setRefreshAddons = useAddonStore((state) => state.setRefreshAddons);

    const ALLOWED_ADDON_STATUSES = [
        ADDONS_STATUS.ACTIVE,
        ADDONS_STATUS.ON_GOING_REQUEST,
        ADDONS_STATUS.INITIAL,
        ADDONS_STATUS.NO_ONGOING_REQUEST,
    ];

    // const userCanBeCancel = keycloakUtils.hasResourceRole(
    //     ADDONS_PERMISSION.CANCEL_ADDONS,
    //     RULES_OF_USER.PERMISSION_TYPE.CUSTOMER
    // );

    const canShowCancelButton = ALLOWED_ADDON_STATUSES.includes(status);

    useEffect(() => {
        getBillCycle();
        if (otpEmailSuccess) {
            setOtpApproved(otpEmailSuccess);
        } else if (otpSmsSuccess) {
            setOtpApproved(otpSmsSuccess);
        }
        if (otpApproved) {
            setOpen(false);
            handleCancelAddOns();
        }
    }, [otpEmailSuccess, otpSmsSuccess, otpApproved]);

    const handleAddonClick = () => {
        setIsOpen(true);
    };

    const handleCancelAddOns = () => {
        // eslint-disable-next-line no-console
        console.log("accountsCbs", accountsCbs);
        // const billiCiclyEndDate = accountsCbs?.account[0].acctInfo.billCycleEndDate;
        // const billiCiclyOpenDate = accountsCbs?.account[0].acctInfo.billCycleOpenDate;
        // const _billiCiclyEndDate = dayjs(billiCiclyEndDate, "YYYYMMDDHHmmss").toDate();
        // const _billiCiclyOpenDate = dayjs(billiCiclyOpenDate, "YYYYMMDDHHmmss").toDate();

        // const cancelVal = validateCancelAddOns(activatedAt, status, _billiCiclyEndDate, _billiCiclyOpenDate);
        const cancelVal = true;
        setIsCancellable(cancelVal);
        // if (cancelVal) {
        //     setCancelMessage(
        //         t("customer:messageCancelAddOnSubtitle1", {
        //             actualBillCycle: calculateCycleDate(Number(accountsCbs?.account[0].acctInfo.billCycleType)).format(
        //                 "DD/MM/YYYY"
        //             ),
        //         })
        //     );
        // } else {
        //     setCancelMessage(t("customer:addonCanotBeCancelled"));
        // }
        setShowCancelDialog(true);
    };

    const handleCancelCloseAddOns = async (): Promise<string | number> => {
        try {

            const serviceId = addOnsServices.collection.find((item) => item.id === id)?.serviceId;
            const addon = addOnsServices.collection.find((item) => item.id === id);

            if (addon) {
                await handleAddClick(addon);
            }
            if (!isEmpty(serviceId)) {
                return "Service ID not found";
            }

            //console.log(" ### AddOns.tsx handleCancelCloseAddOns id", id);
            //console.log(" ### AddOns.tsx handleCancelCloseAddOns serviceId", serviceId);
            //console.log(" ### AddOns.tsx handleCancelCloseAddOns addon", addon);
            
            const closeVal = "";

            //Cuando el addon sea del itemGroupCode MATERIAL, se debe llamar el componente de visita tecnica
            if(addon?.itemGroupCode === "MATERIAL"){
                //console.log(" ### AddOns.tsx handleCancelCloseAddOns Necesita visita tecnica. ###");
                setIsMaterial(false);
            
                callWfeFacadeCancelationAddon({
                    contactDetails: contactDetails,
                    subscriptionsList: subscriptions?.collection ?? [],
                    appointmentConfirmation: true,
                    addonId: id,
                    serviceId: serviceId as number,
                    setOrderIdForSchedule: setOrderIdForSchedule,
                });
            
                setOrderCancelled(closeVal);
                setIsModalOpen(true);

            }else{
                setIsMaterial(false);
                //Metodo para cancelar un addon de tipo servicio
                const closeVal = await removeAddOn(id, serviceId as number, dayjs().format("YYYY-MM-DDTHH:mm:ssZ"));
             
                setOrderCancelled(closeVal);
                setIsModalOpen(true);
            }            

            return closeVal;
        } catch (error: any) {
            setSnackBarError(error.message);

            return error.message;
        }
    };

    return (
        <Grid alignItems="center" container direction="row" key={id} pb={1} pt={0.5} spacing={1}>
            <HandleErrorAndLoading
                error={billCycleState.error}
                isLoading={Boolean(billCycleState.isLoading)}
                skeleton={<Skeleton className={classes.skeleton} height={40} variant="rectangular" width={200} />}
            >
                <Grid item xs={4}>
                    <Tooltip title={`ID: ${id}`}>
                        <Box className={classes.subscriptionWrapper}>
                            <Typography
                                className={
                                    status === ADDONS_STATUS.ONGOING_TERMINATION
                                        ? classes.onGoingTerminationAddon
                                        : classes.activeSubscriptionText
                                }
                                variant="subtitle2"
                            >
                                {description}
                            </Typography>
                        </Box>
                    </Tooltip>
                </Grid>
                {status !== ADDONS_STATUS.ONGOING_TERMINATION ? (
                    <Grid alignItems="center" container direction="row" item spacing={1} xs={8}>
                        <Grid item xs={3}>
                            <Box textAlign="center">
                                <Typography className={classes.subscriptionLabelText}>Fecha de activación</Typography>
                                <Typography className={classes.subscriptionPriceText}>
                                    {activatedAt ? dayjs(activatedAt).format("DD/MM/YYYY") : "-"}
                                </Typography>
                            </Box>
                        </Grid>
                        <Grid item xs={3}>
                            <Box textAlign="center">
                                <Typography className={classes.subscriptionLabelText}>Precio</Typography>
                                <Typography className={classes.subscriptionPriceText}>
                                    {(() => {
                                        const { cartVatIncluded } = getConfig();
                                        const price = cartVatIncluded
                                            ? charges[0].priceVatIncluded
                                            : charges[0].priceVatExcluded;

                                        return `$${centsToPrice(price)}`;
                                    })()}
                                </Typography>
                            </Box>
                        </Grid>
                        <Grid item xs={!canShowCancelButton ? 4 : 3}>
                            <Button
                                color="secondary"
                                fullWidth
                                sx={{ height: 34, fontSize: "14px" }}
                                variant="contained"
                                onClick={handleAddonClick}
                            >
                                {t("customer:promotion")}
                            </Button>
                        </Grid>
                        {canShowCancelButton && (
                            <Grid item xs={3}>
                                <Button
                                    color="inherit"
                                    fullWidth
                                    sx={{ height: 34, fontSize: "14px" }}
                                    variant="contained"
                                    onClick={() => handleCancelAddOns()}
                                >
                                    {t("customer:cancelSubscriptionAddOns")}
                                </Button>
                            </Grid>
                        )}
                    </Grid>
                ) : (
                    <Grid alignItems="center" container direction="row" item spacing={0} xs={8}>
                        <Grid
                            item
                            sx={{
                                backgroundColor: "#fcfcfc",
                                color: "#1A237E",
                                padding: "7px 9px",
                                borderRadius: "3px",
                                borderColor: "#fcfcfc",
                                alignItems: "center",
                                justifyContent: "center",
                                display: "flex",
                                gap: "8px",
                                boxShadow: "0 2px 6px rgba(0, 0, 0, 0.08)",
                                fontSize: "14px",
                                width: "100%",
                            }}
                            xs={12}
                        >
                            <InfoIcon sx={{ fontSize: 20, color: "#1A237E" }} />
                            <Typography sx={{ fontWeight: 500, fontSize: "14px", color: "inherit" }} variant="body2">
                                {t("customer:ongoingCancellationAddon")}
                            </Typography>
                        </Grid>
                    </Grid>
                )}
                <AddonsDetailDialog
                    description={description}
                    handleClose={() => setIsOpen(false)}
                    open={isOpen}
                    // promitionValue={String(Number(charges[0].priceVatIncluded).toLocaleString("US"))}
                    promitionValue="0"
                    promotionTiming={terminatedAt ? terminatedAt : "-"}
                    startEndPromotion={activatedAt}
                    title={titleDialog}
                />
                <CancelAddOnDialog
                    handleCancel={() => setShowCancelDialog(false)}
                    handleCancelCloseAddOns={() => {
                        handleCancelCloseAddOns();
                    }}
                    isCancellable={isCancellable}
                    message={cancelMessage}
                    open={showCancelDialog}
                    orderCancelled={orderCancelled}
                    title={t("customer:titleCancelAddon")}
                />
                <ReScheduleConfirmationModal
                    isOpen={isModalOpen}
                    message={t("common:cancelAddondsRequestLabel")}
                    onClose={() => {
                        setIsModalOpen(false);
                        setShowCancelDialog(false);
                        setRefreshAddons(true);
                        handleShowSchedule(isMaterial);
                    }}
                />
            </HandleErrorAndLoading>
        </Grid>
    );
}
